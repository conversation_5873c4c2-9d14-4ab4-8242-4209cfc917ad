/* Estilos para o modal de perfil público */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 1000;
  padding-top: 80px;
  overflow-y: auto;
}

.modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  padding: 0;
  width: 100%;
  max-width: 600px;
  max-height: calc(100vh - 100px);
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  flex-shrink: 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.title {
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 24px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.content {
  padding: 30px;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

/* Estados de carregamento e erro */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #ffffff;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  text-align: center;
  padding: 40px;
  color: #ff6b6b;
}

.retryButton {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 15px;
  transition: background 0.2s ease;
}

.retryButton:hover {
  background: #45a049;
}

/* Conteúdo do perfil */
.profileContent {
  color: #ffffff;
}

.profileHeader {
  display: flex;
  gap: 25px;
  margin-bottom: 30px;
  align-items: flex-start;
}

.avatarSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.onlineStatus {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #cccccc;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.statusDot.online {
  background: #4CAF50;
  box-shadow: 0 0 6px #4CAF50;
}

.statusDot.offline {
  background: #666666;
}

.profileInfo {
  flex: 1;
}

.displayName {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 5px 0;
  color: #ffffff;
}

.username {
  font-size: 16px;
  color: #888888;
  margin: 0 0 15px 0;
}

.title {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #000000;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 10px;
}

.level {
  font-size: 16px;
  color: #4CAF50;
  font-weight: 600;
  margin-bottom: 15px;
}

.bio {
  font-size: 14px;
  color: #cccccc;
  line-height: 1.5;
  margin: 0;
}

/* Ações */
.actions {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.addFriendButton {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.addFriendButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.friendBadge {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
}

.friendsSince {
  display: block;
  font-size: 12px;
  opacity: 0.8;
  margin-top: 4px;
}

.loginPrompt {
  text-align: center;
  color: #888888;
  font-style: italic;
  margin: 0;
}

/* Seções de informações */
.statsSection,
.achievementsSection,
.badgesSection {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.statsSection h4,
.achievementsSection h4,
.badgesSection h4 {
  margin: 0 0 15px 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.statItem {
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.statValue {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #4CAF50;
  margin-bottom: 5px;
}

.statLabel {
  font-size: 12px;
  color: #cccccc;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.averageScore {
  text-align: center;
  color: #cccccc;
  font-size: 14px;
}

.achievementsList,
.badgesList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.achievementItem {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.achievementIcon {
  font-size: 16px;
}

.badgeItem {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.moreAchievements {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  color: #888888;
  display: flex;
  align-items: center;
}

.additionalInfo {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.joinDate {
  color: #888888;
  font-size: 14px;
  margin: 0;
}

/* Responsividade */
@media (max-width: 768px) {
  .overlay {
    padding-top: 40px;
  }
  
  .modal {
    max-width: none;
    margin: 10px;
    max-height: calc(100vh - 60px);
  }
  
  .header {
    padding: 20px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .content {
    padding: 20px;
  }
  
  .profileHeader {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 20px;
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}
